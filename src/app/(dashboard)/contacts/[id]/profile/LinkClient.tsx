import { FullClient } from '@/shared/interface/clients';
import { GetSingleClientHookReturnType } from './email';
import CustomSelect from '@/components/Input/CustomSelect';
import {
  Box,
  Center,
  Flex,
  HStack,
  Stack,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import StringInput from '@/components/Input/StringInput';
import SearchContact from '@/components/elements/search/SearchContact';
import { useLinkClient } from './useLinkClient';

import { CustomModal } from '@/components/elements/modal/custom-modal';
import { Button } from '@/components/ui/button';
import { FaPlus } from 'react-icons/fa';

export default function LinkClient({
  data,
  getClientHook,
  section,
  variant = 1,
}: {
  getClientHook: GetSingleClientHookReturnType;
  data: FullClient;
  section?: any;
  variant?: number;
}) {
  const linkClientDisclosure = useDisclosure();

  const {
    selectExistingUser,
    form,
    setForm,
    loading,
    handleSubmit,
    relationshipOptions,
    searchResult,
    setSearchResult,
    canProceed,
    isNewClient,
    setIsNewClient,
  } = useLinkClient({
    data,
    getClientHook,
    linkClientDisclosure,
  });

  return (
    <Box width={'100%'} display={'flex'} justifyContent={'flex-end'}>
      {section === 'profile' ? (
        <Center
          cursor="pointer"
          onClick={linkClientDisclosure.onOpen}
          h="20px"
          w="20px"
          bg={variant === 1 ? 'primary.500' : undefined}
          rounded={variant === 1 ? undefined : 'full'}
          _hover={{ bg: 'gray.50' }}
        >
          <FaPlus
            size={variant === 1 ? 10 : 14}
            color={variant === 1 ? 'white' : 'black'}
          />
        </Center>
      ) : (
        <Text onClick={linkClientDisclosure.onOpen}>Add Linked Client</Text>
      )}
      <CustomModal
        w={{ base: '90%', md: '30rem' }}
        open={linkClientDisclosure.open}
        onOpenChange={linkClientDisclosure.onClose}
      >
        <Box pb={'1rem'}>
          <form onSubmit={handleSubmit} className="min-h-[26rem] w-[20rem]">
            <Text textAlign={'center'} mt={2} fontWeight={'bold'}>
              Add Related Client
            </Text>
            <Text fontSize={'.8rem'} textAlign={'center'}>
              Add family members that may be billed under this email
            </Text>

            <Stack gap={'1rem'} my={4}>
              {/* Text-based Toggle */}
              <HStack gap={3}>
                <Text
                  cursor="pointer"
                  fontSize={'1rem'}
                  fontWeight={isNewClient ? 'normal' : 'bold'}
                  borderBottom={'2px solid'}
                  borderColor={isNewClient ? 'transparent' : 'primary.500'}
                  color={isNewClient ? 'gray.500' : 'primary.600'}
                  onClick={() => setIsNewClient(false)}
                  pb={1}
                >
                  Existing Client
                </Text>
                <Text
                  cursor="pointer"
                  fontWeight={isNewClient ? 'bold' : 'normal'}
                  borderBottom={'2px solid'}
                  fontSize={'1rem'}
                  borderColor={!isNewClient ? 'transparent' : 'primary.500'}
                  color={isNewClient ? 'primary.600' : 'gray.500'}
                  onClick={() => setIsNewClient(true)}
                  pb={1}
                >
                  New Client
                </Text>
              </HStack>

              {/* Conditionally Render Based on Selection */}
              {!isNewClient && (
                <Stack mb={'2rem'}>
                  <label className="font-medium text-gray-900">
                    Lookup Client
                  </label>
                  <SearchContact
                    setSearchResult={(e: any) => setSearchResult(e)}
                    searchResult={searchResult}
                    selectExistingUser={selectExistingUser}
                  />
                </Stack>
              )}

              <StringInput
                inputProps={{
                  name: 'firstName',
                  value: form.firstName || undefined,
                  onChange: (e: any) =>
                    setForm({ ...form, firstName: e.target.value }),
                  ...(isNewClient ? {} : { isReadOnly: true }),
                }}
                fieldProps={{ label: 'First name', required: true }}
              />

              <StringInput
                inputProps={{
                  name: 'lastName',
                  value: form.lastName || undefined,
                  onChange: (e: any) =>
                    setForm({ ...form, lastName: e.target.value }),
                  ...(isNewClient ? {} : { isReadOnly: true }),
                }}
                fieldProps={{ label: 'Last name', required: true }}
              />

              {isNewClient && (
                <StringInput
                  inputProps={{
                    name: 'email',
                    type: 'email',
                    value: form.email || undefined,
                    onChange: (e: any) =>
                      setForm({ ...form, email: e.target.value }),
                  }}
                  fieldProps={{ label: 'Email' }}
                />
              )}

              <CustomSelect
                placeholder=""
                options={relationshipOptions}
                onChange={(val) =>
                  setForm({ ...form, relationship: val.value })
                }
                label="Relationship to Client"
                required
              />
            </Stack>

            <Flex justifyContent={'flex-end'} gap={10}>
              <Button
                variant={'subtle'}
                onClick={linkClientDisclosure.onClose}
                bg={'gray'}
              >
                Cancel
              </Button>
              <Button
                bg={'primary.500'}
                loading={loading}
                disabled={!canProceed}
                onClick={handleSubmit}
              >
                Add
              </Button>
            </Flex>
          </form>
        </Box>
      </CustomModal>
    </Box>
  );
}
